"""Beautiful CLI for the Snakey Brainfuck Interpreter.

This module provides a modern, colorful command-line interface for running
Brainfuck programs with rich formatting and helpful error messages.
"""

from pathlib import Path

import typer
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table

from snakey_brainfuck_interpreter.io import I<PERSON>
from snakey_brainfuck_interpreter.jumps import Jumps
from snakey_brainfuck_interpreter.memory import Memory
from snakey_brainfuck_interpreter.parser import Parser

# Initialize Rich console for beautiful output
console = Console()

# Create Typer app
app = typer.Typer(
    name="snakey-bf",
    help="🐍 A beautiful Brainfuck interpreter with a modern CLI interface",
    add_completion=False,
    rich_markup_mode="rich",
)


def validate_file_path(file_path: str) -> Path:
    """Validate that the file path exists and has the correct extension.

    Args:
        file_path: Path to the Brainfuck file

    Returns:
        Path object if valid

    Raises:
        typer.Exit: If file is invalid

    """
    path = Path(file_path)

    # Check if file exists
    if not path.exists():
        console.print(f"[red]❌ Error:[/red] File '{file_path}' not found!")
        raise typer.Exit(1)

    # Check if it's a file (not directory)
    if not path.is_file():
        console.print(f"[red]❌ Error:[/red] '{file_path}' is not a file!")
        raise typer.Exit(1)

    # Check file extension
    if path.suffix.lower() not in [".b", ".bf"]:
        console.print("[red]❌ Error:[/red] File must have .b or .bf extension!")
        console.print(
            "[yellow]💡 Tip:[/yellow] Rename your file to have a .bf extension",
        )
        raise typer.Exit(1)

    return path


def read_brainfuck_file(file_path: Path) -> str:
    """Read and return the contents of a Brainfuck file.

    Args:
        file_path: Path to the Brainfuck file

    Returns:
        File contents as string

    Raises:
        typer.Exit: If file cannot be read

    """
    try:
        content = file_path.read_text(encoding="utf-8")
    except UnicodeDecodeError:
        console.print(
            f"[red]❌ Error:[/red] Cannot read file '{file_path}' - invalid encoding!",
        )
        console.print("[yellow]💡 Tip:[/yellow] Make sure the file is saved as UTF-8")
        raise typer.Exit(1) from None
    except PermissionError:
        console.print(f"[red]❌ Error:[/red] Permission denied reading '{file_path}'!")
        raise typer.Exit(1) from None
    except Exception as e:
        console.print(f"[red]❌ Error:[/red] Failed to read file: {e}")
        raise typer.Exit(1) from e
    else:
        if not content.strip():
            console.print(f"[yellow]⚠️  Warning:[/yellow] File '{file_path}' is empty!")
        return content


def display_file_info(file_path: Path, content: str) -> None:
    """Display beautiful information about the Brainfuck file.

    Args:
        file_path: Path to the file
        content: File content

    """
    # Create info table
    table = Table(title=f"📄 File Information: {file_path.name}")
    table.add_column("Property", style="cyan", no_wrap=True)
    table.add_column("Value", style="white")

    # File stats
    file_size = file_path.stat().st_size
    table.add_row("📁 Path", str(file_path))
    table.add_row("📏 Size", f"{file_size} bytes")
    table.add_row("📝 Lines", str(len(content.splitlines())))
    table.add_row("🔤 Characters", str(len(content)))

    # Brainfuck-specific stats
    parser = Parser(content)
    clean_content = parser.clean_code()
    table.add_row("🧹 Valid BF chars", str(len(clean_content)))
    table.add_row("🗑️  Ignored chars", str(len(content) - len(clean_content)))

    # Count instructions
    instruction_counts = {}
    for char in clean_content:
        instruction_counts[char] = instruction_counts.get(char, 0) + 1

    if instruction_counts:
        instructions_text = ", ".join([
            f"{char}:{count}" for char, count in sorted(instruction_counts.items())
        ])
        table.add_row("🎯 Instructions", instructions_text)

    console.print(table)


# Constants
MAX_CODE_DISPLAY_LENGTH = 200


class BrainfuckRunner:
    """A runner that executes Brainfuck code using the provided components."""

    def __init__(self, memory: Memory, io_handler: IO, jumps: Jumps) -> None:
        """Initialize the runner with the required components.

        Args:
            memory: Memory instance for the tape
            io_handler: IO instance for input/output
            jumps: Jumps instance for bracket handling

        """
        self.memory = memory
        self.io_handler = io_handler
        self.jumps = jumps
        self.position = 0

    def _handle_output(self) -> None:
        """Handle the '.' command - output current memory value as character."""
        char_value = self.memory.get_value()
        console.print(chr(char_value), end="")

    def _handle_input(self) -> None:
        """Handle the ',' command - input character and store ASCII value."""
        try:
            char = input()
            if char:
                self.memory.set_value(ord(char[0]))
            else:
                self.memory.set_value(0)
        except EOFError:
            self.memory.set_value(0)

    def _handle_jump_forward(self) -> None:
        """Handle the '[' command - jump forward if current cell is zero."""
        if self.memory.get_value() == 0:
            self.position = self.jumps.jump_table[self.position]

    def _handle_jump_backward(self) -> None:
        """Handle the ']' command - jump backward if current cell is not zero."""
        if self.memory.get_value() != 0:
            self.position = self.jumps.jump_table[self.position]

    def execute(self, code: str) -> None:
        """Execute the Brainfuck code.

        Args:
            code: The Brainfuck code to execute

        """
        self.position = 0
        while self.position < len(code):
            command = code[self.position]
            if command == ">":
                self.memory.move_right()
            elif command == "<":
                self.memory.move_left()
            elif command == "+":
                self.memory.increment()
            elif command == "-":
                self.memory.decrement()
            elif command == ".":
                self._handle_output()
            elif command == ",":
                self._handle_input()
            elif command == "[":
                self._handle_jump_forward()
            elif command == "]":
                self._handle_jump_backward()

            self.position += 1


@app.command()
def run(
    file_path: str = typer.Argument(
        ..., help="Path to the Brainfuck file (.b or .bf)",
    ),
    *,
    show_info: bool = typer.Option(
        False, "--info", "-i", help="Show file information before running",  # noqa: FBT003
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Show detailed execution information",  # noqa: FBT003
    ),
) -> None:
    """🚀 Run a Brainfuck program from a file.

    This command reads a Brainfuck program from the specified file and executes it.
    The file must have a .b or .bf extension.
    """
    # Validate and read file
    path = validate_file_path(file_path)

    with console.status("[bold green]Reading file...", spinner="dots"):
        content = read_brainfuck_file(path)

    # Show file info if requested
    if show_info:
        display_file_info(path, content)
        console.print()

    # Parse and validate the code
    console.print("[bold blue]🔍 Parsing Brainfuck code...[/bold blue]")
    parser = Parser(content)

    # Check for balanced brackets
    if not parser.check_brackets():
        console.print("[red]❌ Error:[/red] Unbalanced brackets in the code!")
        console.print(
            "[yellow]💡 Tip:[/yellow] Make sure every '[' has a matching ']'",
        )
        raise typer.Exit(1)

    # Clean the code
    clean_code = parser.clean_code()

    if verbose:
        console.print(f"[dim]Original code length: {len(content)} characters[/dim]")
        console.print(f"[dim]Clean code length: {len(clean_code)} characters[/dim]")
        console.print()

    # Show syntax-highlighted code if it's short enough
    if len(clean_code) <= MAX_CODE_DISPLAY_LENGTH and verbose:
        syntax = Syntax(clean_code, "brainfuck", theme="monokai", line_numbers=False)
        console.print(Panel(syntax, title="🧠 Brainfuck Code", border_style="blue"))
        console.print()

    # Initialize components
    memory = Memory()
    io_handler = IO()
    jumps = Jumps(clean_code)
    jumps.build(clean_code)
    runner = BrainfuckRunner(memory, io_handler, jumps)

    # Execute the program
    console.print("[bold green]🚀 Executing Brainfuck program...[/bold green]")
    console.print("[dim]Output:[/dim]")
    console.print("─" * 50)

    try:
        runner.execute(clean_code)
        console.print()
        console.print("─" * 50)
        console.print("[bold green]✅ Program completed successfully![/bold green]")

    except KeyboardInterrupt:
        console.print()
        console.print("─" * 50)
        console.print("[yellow]⚠️  Program interrupted by user[/yellow]")
        raise typer.Exit(130) from None
    except Exception as e:
        console.print()
        console.print("─" * 50)
        console.print(f"[red]❌ Runtime error: {e}[/red]")
        raise typer.Exit(1) from e


@app.command()
def info(
    file_path: str = typer.Argument(
        ..., help="Path to the Brainfuck file (.b or .bf)",
    ),
) -> None:
    """📊 Show detailed information about a Brainfuck file.

    This command analyzes a Brainfuck file and displays comprehensive
    information about its contents, structure, and statistics.
    """
    # Validate and read file
    path = validate_file_path(file_path)

    with console.status("[bold green]Analyzing file...", spinner="dots"):
        content = read_brainfuck_file(path)

    # Display comprehensive file information
    display_file_info(path, content)

    # Show code preview if it's not too long
    parser = Parser(content)
    clean_code = parser.clean_code()

    if clean_code and len(clean_code) <= MAX_CODE_DISPLAY_LENGTH:
        console.print()
        syntax = Syntax(clean_code, "brainfuck", theme="monokai", line_numbers=True)
        console.print(Panel(
            syntax, title="🧠 Brainfuck Code Preview", border_style="blue",
        ))

    # Check for potential issues
    console.print()
    issues_found = False

    if not parser.check_brackets():
        console.print("[red]⚠️  Issue found: Unbalanced brackets![/red]")
        issues_found = True

    if not clean_code.strip():
        console.print(
            "[yellow]⚠️  Issue found: No valid Brainfuck instructions![/yellow]",
        )
        issues_found = True

    if not issues_found:
        console.print("[green]✅ No issues found - file looks good![/green]")


@app.command()
def validate(
    file_path: str = typer.Argument(
        ..., help="Path to the Brainfuck file (.b or .bf)",
    ),
) -> None:
    """✅ Validate a Brainfuck file for syntax errors.

    This command checks if a Brainfuck file has valid syntax,
    particularly focusing on balanced brackets.
    """
    # Validate and read file
    path = validate_file_path(file_path)

    with console.status("[bold green]Validating file...", spinner="dots"):
        content = read_brainfuck_file(path)

    console.print(f"[bold blue]🔍 Validating {path.name}...[/bold blue]")

    parser = Parser(content)
    clean_code = parser.clean_code()

    # Check for issues
    issues = []

    if not parser.check_brackets():
        issues.append("Unbalanced brackets")

    if not clean_code.strip():
        issues.append("No valid Brainfuck instructions found")

    # Display results
    if issues:
        console.print(f"[red]❌ Validation failed! Found {len(issues)} issue(s):[/red]")
        for i, issue in enumerate(issues, 1):
            console.print(f"  {i}. {issue}")
        raise typer.Exit(1)

    console.print(
        "[green]✅ Validation passed! File is syntactically correct.[/green]",
    )
    console.print(
        f"[dim]Found {len(clean_code)} valid Brainfuck instructions.[/dim]",
    )


@app.command()
def version() -> None:
    """📋 Show version information."""
    console.print(
        Panel.fit(
            "[bold blue]🐍 Snakey Brainfuck Interpreter[/bold blue]\n"
            "[dim]Version:[/dim] [green]0.1.0[/green]\n"
            "[dim]Author:[/dim] KevinTadashiii\n"
            "[dim]License:[/dim] MIT",
            border_style="blue",
            title="Version Info",
        ),
    )


def main() -> None:
    """Entry point for the CLI application."""
    app()


if __name__ == "__main__":
    main()
